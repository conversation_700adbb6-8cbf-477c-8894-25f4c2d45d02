package com.ybda.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.util.SaResult;
import com.ybda.model.dto.SysRoleMenuIdsDTO;
import com.ybda.model.entity.SysMenu;
import com.ybda.service.SysMenuService;
import com.ybda.service.SysRoleMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
/**
 * 菜单接口
 */
@RestController
@RequestMapping("/menu")
public class SysMenuController {
    @Autowired
    SysMenuService sysMenuService;
    @Autowired
    private SysRoleMenuService sysRoleMenuService;

    /**
     * 菜单树
     * @return
     */
    @SaCheckPermission("sys:menu:view")
    @GetMapping("/tree")
    public SaResult tree() {
        return sysMenuService.tree();
    }

    /**
     * 根据角色ID获取对应的菜单权限
     * @return
     */
    @SaCheckPermission("sys:menu:view")
    @GetMapping("/getRoleMenuIds")
    public SaResult getMenuListByRoleId(Integer roleId) {
        return sysRoleMenuService.getMenuListByRoleId(roleId);
    }

    /**
     * 修改角色的菜单权限
     * @return
     */
    @SaCheckPermission("sys:role:menu:add")
    @PostMapping("/updateRoleMenuIds")
    public SaResult updateRoleMenuIds(@RequestBody SysRoleMenuIdsDTO sysRoleMenuIdsDTO) {
        return sysRoleMenuService.updateRoleMenuIds(sysRoleMenuIdsDTO);
    }

    /**
     * 获取根据菜单ID获取详细菜单详细
     * @return
     */
    @SaCheckPermission("sys:menu:view")
    @GetMapping("/getEditMenuInfo")
    public SaResult getEditMenuInfo(Integer id) {
        return sysMenuService.getEditMenuInfo(id);
    }

    /**
     * 根据菜单ID修改菜单详细
     */
    @SaCheckPermission("sys:menu:update")
    @PostMapping("/updateMenuInfo")
    public SaResult updateMenuInfo(@RequestBody SysMenu menu) {
        sysMenuService.updateById(menu);
        return SaResult.ok();
    }

    /**
     * 删除菜单
     */
    @SaCheckPermission("sys:menu:delete")
    @DeleteMapping("/deleteMenu")
    public SaResult deleteMenu(Integer id) {
        sysMenuService.removeById(id);
        return SaResult.ok();
    }

    /**
     * 所有菜单路由名称
     */
    @SaCheckPermission("sys:menu:view")
    @GetMapping("/getAllPages")
    public SaResult getAllPages() {
        return sysMenuService.getAllPages();
    }
    /**
     * 获取菜单权限
     */
    @SaCheckPermission("sys:permission:view")
    @GetMapping("/getMenuPermission")
    public SaResult getMenuPermission() {
        return sysMenuService.queryMenuPermissionList();
    }

    /**
     * 根据角色ID获取菜单权限
     */
    @SaCheckPermission("sys:role:view")
    @GetMapping("/getMenuPermissionByRoleId")
    public SaResult getMenuPermissionByRoleId(Integer roleId) {
        return sysMenuService.getMenuPermissionByRoleId(roleId);
    }
    /**
     * 新增菜单
     */
    @SaCheckPermission("sys:menu:add")
    @PostMapping("/addMenu")
    public SaResult addMenu(@RequestBody SysMenu menu) {
        sysMenuService.save(menu);
        return SaResult.ok();
    }

}
