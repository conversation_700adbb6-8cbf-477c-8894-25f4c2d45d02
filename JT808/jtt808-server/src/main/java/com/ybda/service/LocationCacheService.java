package com.ybda.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ybda.model.entity.DeviceLocation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 设备位置信息Redis缓存服务
 * 负责设备最新位置信息的缓存管理，提升查询性能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LocationCacheService {
    
    private final StringRedisTemplate redisTemplate;
    private final ObjectMapper objectMapper;
    
    // 缓存key前缀
    private static final String DEVICE_LOCATION_PREFIX = "device:location:latest:";
    private static final String ALL_DEVICES_LOCATION_KEY = "device:location:all:latest";
    
    // 缓存过期时间（30分钟）
    private static final long CACHE_EXPIRE_MINUTES = 30;
    
    /**
     * 缓存设备最新位置信息
     * @param deviceId 设备ID
     * @param location 位置信息
     */
    public void cacheLatestLocation(String deviceId, DeviceLocation location) {
        try {
            String key = DEVICE_LOCATION_PREFIX + deviceId;
            String locationJson = objectMapper.writeValueAsString(location);
            
            // 设置单个设备位置缓存，30分钟过期
            redisTemplate.opsForValue().set(key, locationJson, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
            
            // 同时更新全局设备位置集合（使用Hash结构）
            redisTemplate.opsForHash().put(ALL_DEVICES_LOCATION_KEY, deviceId, locationJson);
            redisTemplate.expire(ALL_DEVICES_LOCATION_KEY, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
            
            log.debug("缓存设备最新位置: deviceId={}, time={}, lng={}, lat={}", 
                deviceId, location.getDeviceTime(), location.getLng(), location.getLat());
                
        } catch (Exception e) {
            log.error("缓存设备位置失败: deviceId={}, error={}", deviceId, e.getMessage(), e);
        }
    }
    
    /**
     * 从缓存获取设备最新位置
     * @param deviceId 设备ID
     * @return 位置信息，缓存未命中返回null
     */
    public DeviceLocation getLatestLocationFromCache(String deviceId) {
        try {
            String key = DEVICE_LOCATION_PREFIX + deviceId;
            String locationJson = redisTemplate.opsForValue().get(key);
            
            if (locationJson != null) {
                DeviceLocation location = objectMapper.readValue(locationJson, DeviceLocation.class);
                log.debug("从缓存获取设备位置: deviceId={}, time={}", deviceId, location.getDeviceTime());
                return location;
            }
            
            log.debug("设备位置缓存未命中: deviceId={}", deviceId);
            return null;
        } catch (Exception e) {
            log.error("获取设备位置缓存失败: deviceId={}, error={}", deviceId, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 获取所有设备的最新位置（从缓存）
     * @return 设备ID -> 位置信息的映射
     */
    public Map<String, DeviceLocation> getAllLatestLocationsFromCache() {
        try {
            Map<Object, Object> allLocationsJson = redisTemplate.opsForHash().entries(ALL_DEVICES_LOCATION_KEY);
            Map<String, DeviceLocation> result = new HashMap<>();
            
            for (Map.Entry<Object, Object> entry : allLocationsJson.entrySet()) {
                String deviceId = (String) entry.getKey();
                String locationJson = (String) entry.getValue();
                
                try {
                    DeviceLocation location = objectMapper.readValue(locationJson, DeviceLocation.class);
                    result.put(deviceId, location);
                } catch (Exception e) {
                    log.warn("解析设备位置缓存失败: deviceId={}, error={}", deviceId, e.getMessage());
                }
            }
            
            log.debug("从缓存获取所有设备位置: count={}", result.size());
            return result;
        } catch (Exception e) {
            log.error("获取所有设备位置缓存失败: error={}", e.getMessage(), e);
            return new HashMap<>();
        }
    }
    
    /**
     * 删除设备位置缓存
     * @param deviceId 设备ID
     */
    public void removeLocationCache(String deviceId) {
        try {
            String key = DEVICE_LOCATION_PREFIX + deviceId;
            redisTemplate.delete(key);
            redisTemplate.opsForHash().delete(ALL_DEVICES_LOCATION_KEY, deviceId);
            
            log.debug("删除设备位置缓存: deviceId={}", deviceId);
        } catch (Exception e) {
            log.error("删除设备位置缓存失败: deviceId={}, error={}", deviceId, e.getMessage(), e);
        }
    }
    
    /**
     * 检查设备位置缓存是否存在
     * @param deviceId 设备ID
     * @return 是否存在
     */
    public boolean hasLocationCache(String deviceId) {
        try {
            String key = DEVICE_LOCATION_PREFIX + deviceId;
            return Boolean.TRUE.equals(redisTemplate.hasKey(key));
        } catch (Exception e) {
            log.error("检查设备位置缓存失败: deviceId={}, error={}", deviceId, e.getMessage(), e);
            return false;
        }
    }
}
