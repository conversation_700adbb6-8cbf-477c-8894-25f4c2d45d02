package com.ybda.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ybda.util.RedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * JT808服务权限缓存服务
 * 负责用户权限和角色的Redis缓存管理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PermissionCacheService {
    @Autowired
    RedisUtils redisUtils;

    private final ObjectMapper objectMapper;
    
    // 缓存key前缀
    private static final String USER_PERMISSIONS_PREFIX = "user:permissions:";
    private static final String USER_ROLES_PREFIX = "user:roles:";
    
    // 缓存过期时间（30分钟）
    private static final long CACHE_EXPIRE_MINUTES = 30;
    
    /**
     * 获取用户权限缓存
     * @param userId 用户ID
     * @return 权限列表，如果缓存不存在返回null
     */
    public List<String> getUserPermissionsFromCache(String userId) {
        try {
            String key = USER_PERMISSIONS_PREFIX + userId;
            String cacheValue = redisUtils.get(key);
            
            if (cacheValue != null) {
                log.debug("JT808从缓存获取用户权限: userId={}", userId);
                return objectMapper.readValue(cacheValue, new TypeReference<List<String>>() {});
            }
            
            log.debug("JT808用户权限缓存未命中: userId={}", userId);
            return null;
        } catch (Exception e) {
            log.error("JT808获取用户权限缓存失败: userId={}", userId, e);
            return null;
        }
    }
    
    /**
     * 缓存用户权限
     * @param userId 用户ID
     * @param permissions 权限列表
     */
    public void cacheUserPermissions(String userId, List<String> permissions) {
        try {
            String key = USER_PERMISSIONS_PREFIX + userId;
            String value = objectMapper.writeValueAsString(permissions != null ? permissions : new ArrayList<>());
            redisUtils.setEx(key, value, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
            log.debug("JT808缓存用户权限: userId={}, permissions={}", userId, permissions);
        } catch (Exception e) {
            log.error("JT808缓存用户权限失败: userId={}", userId, e);
        }
    }
    
    /**
     * 获取用户角色缓存
     * @param userId 用户ID
     * @return 角色列表，如果缓存不存在返回null
     */
    public List<String> getUserRolesFromCache(String userId) {
        try {
            String key = USER_ROLES_PREFIX + userId;
            String cacheValue = redisUtils.get(key);
            
            if (cacheValue != null) {
                log.debug("JT808从缓存获取用户角色: userId={}", userId);
                return objectMapper.readValue(cacheValue, new TypeReference<List<String>>() {});
            }
            
            log.debug("JT808用户角色缓存未命中: userId={}", userId);
            return null;
        } catch (Exception e) {
            log.error("JT808获取用户角色缓存失败: userId={}", userId, e);
            return null;
        }
    }
    
    /**
     * 缓存用户角色
     * @param userId 用户ID
     * @param roles 角色列表
     */
    public void cacheUserRoles(String userId, List<String> roles) {
        try {
            String key = USER_ROLES_PREFIX + userId;
            String value = objectMapper.writeValueAsString(roles != null ? roles : new ArrayList<>());
            redisUtils.setEx(key, value, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
            log.debug("JT808缓存用户角色: userId={}, roles={}", userId, roles);
        } catch (Exception e) {
            log.error("JT808缓存用户角色失败: userId={}", userId, e);
        }
    }
    
    /**
     * 清除指定用户的权限和角色缓存
     * @param userId 用户ID
     */
    public void clearUserCache(String userId) {
        try {
            String permissionKey = USER_PERMISSIONS_PREFIX + userId;
            String roleKey = USER_ROLES_PREFIX + userId;
            redisUtils.delete(permissionKey);
            redisUtils.delete(roleKey);
            log.info("JT808清除用户缓存: userId={}", userId);
        } catch (Exception e) {
            log.error("JT808清除用户缓存失败: userId={}", userId, e);
        }
    }
}
