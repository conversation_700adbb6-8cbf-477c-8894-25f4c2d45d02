package com.ybda.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 设备位置信息VO - 前端展示
 */
@Data
@Accessors(chain = true)
public class DeviceLocationVO {

    /** 位置记录ID */
    private String id;

    /** 设备ID */
    private String deviceId;

    /** 设备手机号 */
    private String mobileNo;

    /** 车牌号 */
    private String plateNo;

    /** 设备名称 */
    private String deviceName;

    /** 实际纬度（度） */
    private Double lat;

    /** 实际经度（度） */
    private Double lng;

    /** 高程（米） */
    private Integer altitude;

    /** 实际速度（公里每小时） */
    private Double speedKph;

    /** 方向（0-359度，正北为0，顺时针） */
    private Integer direction;

    /** 设备时间（GPS时间） */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deviceTime;

    /** 地址信息 */
    private String address;

    /** 省份 */
    private String province;

    /** 城市 */
    private String city;

    /** 区县 */
    private String district;

    /** 街道 */
    private String street;

    /** 是否有效位置（GPS定位状态） */
    private Boolean isValid;

    /** 卫星数量 */
    private Integer satelliteCount;

    /** 报警状态 */
    private Boolean hasAlarm;

    /** 报警信息 */
    private String alarmInfo;

    /** 车辆状态 */
    private String vehicleStatus;

    /** 数据接收时间（服务器时间） */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime receiveTime;

    /** 位置附加信息（简化版，只包含重要信息） */
    private Map<String, Object> extraInfo;

    /**
     * 获取定位状态描述
     */
    public String getLocationStatus() {
        if (isValid != null && isValid) {
            return "GPS定位";
        } else {
            return "基站定位";
        }
    }

    /**
     * 获取速度状态描述
     */
    public String getSpeedStatus() {
        if (speedKph == null || speedKph <= 0) {
            return "静止";
        } else if (speedKph <= 5) {
            return "缓行";
        } else if (speedKph <= 60) {
            return "正常";
        } else {
            return "高速";
        }
    }

    /**
     * 获取方向描述
     */
    public String getDirectionDesc() {
        if (direction == null) {
            return "未知";
        }
        
        String[] directions = {"北", "东北", "东", "东南", "南", "西南", "西", "西北"};
        int index = (int) Math.round(direction / 45.0) % 8;
        return directions[index];
    }

    /**
     * 获取信号强度描述
     */
    public String getSignalStrength() {
        if (satelliteCount == null) {
            return "未知";
        } else if (satelliteCount >= 8) {
            return "强";
        } else if (satelliteCount >= 5) {
            return "中";
        } else if (satelliteCount >= 3) {
            return "弱";
        } else {
            return "很弱";
        }
    }
}
