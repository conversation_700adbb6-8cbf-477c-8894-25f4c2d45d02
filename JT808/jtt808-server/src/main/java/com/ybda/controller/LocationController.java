package com.ybda.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.util.SaResult;
import com.ybda.model.entity.DeviceLocation;
import com.ybda.service.LocationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 位置信息Controller
 * 提供设备位置查询和轨迹分析接口
 */
@Slf4j
@RestController
@RequestMapping("/location")
@RequiredArgsConstructor
@Validated
public class LocationController {

    private final LocationService locationService;

    /**
     * 获取设备最新位置
     */
//    @SaCheckPermission("jt808:location:view")
    @GetMapping("/latest/{deviceId}")
    public SaResult getLatestLocation(@PathVariable String deviceId) {
        try {
            DeviceLocation location = locationService.getLatestLocation(deviceId);
            if (location != null) {
                return SaResult.data(location);
            } else {
                return SaResult.error("未找到设备位置信息");
            }
        } catch (Exception e) {
            log.error("获取设备最新位置失败: deviceId={}", deviceId, e);
            return SaResult.error("获取位置信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取所有启用设备的最新位置（地图展示）
     * 包括在线设备的实时位置和离线设备的最后上报位置
     */
//    @SaCheckPermission("jt808:location:view")
    @GetMapping("/online/all")
    public SaResult getAllOnlineDeviceLocations() {
        try {
            List<DeviceLocation> locations = locationService.getAllEnabledDeviceLocations();
            return SaResult.data(locations);
        } catch (Exception e) {
            log.error("获取设备位置失败", e);
            return SaResult.error("获取设备位置失败：" + e.getMessage());
        }
    }

    /**
     * 获取设备轨迹（指定时间范围）
     */
//    @SaCheckPermission("jt808:location:view")
    @GetMapping("/track/{deviceId}")
    public SaResult getDeviceTrack(
            @PathVariable String deviceId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @RequestParam(defaultValue = "1000") int limit) {
        try {
            List<DeviceLocation> track = locationService.getDeviceTrack(deviceId, startTime, endTime, limit);
            return SaResult.data(track);
        } catch (Exception e) {
            log.error("获取设备轨迹失败: deviceId={}, start={}, end={}", 
                deviceId, startTime, endTime, e);
            return SaResult.error("获取设备轨迹失败：" + e.getMessage());
        }
    }

    /**
     * 获取设备最近轨迹
     */
//    @SaCheckPermission("jt808:location:view")
    @GetMapping("/track/recent/{deviceId}")
    public SaResult getRecentTrack(
            @PathVariable String deviceId,
            @RequestParam(defaultValue = "100") int count) {
        try {
            List<DeviceLocation> track = locationService.getRecentTrack(deviceId, count);
            return SaResult.data(track);
        } catch (Exception e) {
            log.error("获取设备最近轨迹失败: deviceId={}, count={}", deviceId, count, e);
            return SaResult.error("获取最近轨迹失败：" + e.getMessage());
        }
    }

    /**
     * 获取指定区域内的设备位置
     */
//    @SaCheckPermission("jt808:location:view")
    @GetMapping("/area")
    public SaResult getLocationsByArea(
            @RequestParam double minLng,
            @RequestParam double maxLng,
            @RequestParam double minLat,
            @RequestParam double maxLat,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        try {
            List<DeviceLocation> locations = locationService.getLocationsByArea(
                minLng, maxLng, minLat, maxLat, startTime, endTime);
            return SaResult.data(locations);
        } catch (Exception e) {
            log.error("获取区域位置失败: area=[{},{},{},{}]", minLng, maxLng, minLat, maxLat, e);
            return SaResult.error("获取区域位置失败：" + e.getMessage());
        }
    }

    /**
     * 统计位置记录数量
     */
//    @SaCheckPermission("jt808:location:view")
    @GetMapping("/count")
    public SaResult countLocationRecords(
            @RequestParam(required = false) String deviceId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        try {
            long count = locationService.countLocationRecords(deviceId, startTime, endTime);
            return SaResult.data(count);
        } catch (Exception e) {
            log.error("统计位置记录失败: deviceId={}", deviceId, e);
            return SaResult.error("统计位置记录失败：" + e.getMessage());
        }
    }

    /**
     * 清理历史位置数据（管理员功能）
     */
//    @SaCheckPermission("jt808:location:delete")
    @DeleteMapping("/clean")
    public SaResult cleanHistoryLocations(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime beforeTime) {
        try {
            long deletedCount = locationService.cleanHistoryLocations(beforeTime);
            return SaResult.data("清理完成，删除记录数：" + deletedCount);
        } catch (Exception e) {
            log.error("清理历史位置数据失败: beforeTime={}", beforeTime, e);
            return SaResult.error("清理历史数据失败：" + e.getMessage());
        }
    }

    /**
     * 逆地理编码 - 根据经纬度获取地址
     */
//    @SaCheckPermission("jt808:location:view")
    @GetMapping("/geocode")
    public SaResult reverseGeocode(
            @RequestParam double lng,
            @RequestParam double lat) {
        try {
            String address = locationService.reverseGeocode(lng, lat);
            return SaResult.data(address);
        } catch (Exception e) {
            log.error("逆地理编码失败: lng={}, lat={}", lng, lat, e);
            return SaResult.error("地址解析失败：" + e.getMessage());
        }
    }
}
