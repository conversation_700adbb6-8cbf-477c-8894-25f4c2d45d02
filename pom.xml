<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.2.5</version>
        <relativePath/>
    </parent>
    <groupId>com.ybda</groupId>
    <artifactId>AssetInspection</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>AssetInspection</name>
    <description>AssetInspection</description>
    <url/>
    <licenses>
        <license/>
    </licenses>
    <developers>
        <developer/>
    </developers>
    <modules>
        <module>gateway</module>
        <module>auth-service</module>
        <module>JT808</module>
        <module>analysis-service</module>
    </modules>
    <scm>
        <connection/>
        <developerConnection/>
        <tag/>
        <url/>
    </scm>
    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <!-- 依赖版本统一管理 -->
        <spring-cloud-alibaba.version>2023.0.3.3</spring-cloud-alibaba.version>
        <spring-cloud.version>2023.0.3</spring-cloud.version>
        <sa-token.version>1.43.0</sa-token.version>
        <mybatis-plus.version>3.5.6</mybatis-plus.version>
        <mysql-connector.version>8.0.33</mysql-connector.version>
        <hutool.version>5.8.6</hutool.version>
        <fastjson.version>1.2.28</fastjson.version>

        <protostar.version>4.0.3</protostar.version>
        <netmc.version>4.0.3</netmc.version>
        <mybatis-typehandlers.version>1.0.2</mybatis-typehandlers.version>
        <jakarta-annotation.version>2.1.0</jakarta-annotation.version>

        <guava.version>33.2.0-jre</guava.version>
        <mariadb.version>3.4.1</mariadb.version>
        <h2.version>2.3.232</h2.version>
        <gson.version>2.10.1</gson.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Cloud Alibaba BOM -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- Spring Cloud BOM -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 项目内部模块版本管理 -->

            <!-- JT808内部模块版本管理 -->
            <dependency>
                <groupId>com.ybda</groupId>
                <artifactId>jtt808-protocol</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ybda</groupId>
                <artifactId>jtt808-server</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- Sa-Token 相关依赖版本统一 -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot3-starter</artifactId>
                <version>${sa-token.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-redis-template</artifactId>
                <version>${sa-token.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-reactor-spring-boot3-starter</artifactId>
                <version>${sa-token.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-redis-jackson</artifactId>
                <version>${sa-token.version}</version>
            </dependency>

            <!-- 数据库相关依赖版本统一 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-bom</artifactId>
                <version>${mybatis-plus.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql-connector.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mariadb.jdbc</groupId>
                <artifactId>mariadb-java-client</artifactId>
                <version>${mariadb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.h2database</groupId>
                <artifactId>h2</artifactId>
                <version>${h2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-typehandlers-jsr310</artifactId>
                <version>${mybatis-typehandlers.version}</version>
            </dependency>

            <!-- 工具类依赖版本统一 -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.annotation</groupId>
                <artifactId>jakarta.annotation-api</artifactId>
                <version>${jakarta-annotation.version}</version>
            </dependency>



            <!-- JT808相关依赖 -->
            <dependency>
                <groupId>io.github.yezhihao</groupId>
                <artifactId>protostar</artifactId>
                <version>${protostar.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.yezhihao</groupId>
                <artifactId>netmc</artifactId>
                <version>${netmc.version}</version>
            </dependency>

            <!-- 其他工具依赖 -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- lombok 依赖，供所有子模块编译期使用 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>central</id>
            <url>https://repo.maven.apache.org/maven2/</url>
        </repository>
    </repositories>

</project>
